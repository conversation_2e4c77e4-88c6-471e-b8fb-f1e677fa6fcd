{"permissions": {"allow": ["Bash(npm install @mswjs/interceptors jsondiffpatch)", "Bash(npm run compile)", "Bash(npm run build)", "Bash(grep -n \"ToolManager\" /Users/<USER>/Documents/zyb/extension/fwyy-tools/entrypoints/popup/main.ts)", "Bash(grep -n \"^  }\" /Users/<USER>/Documents/zyb/extension/fwyy-tools/entrypoints/popup/main.ts)", "Bash(awk '/^class ToolManager/,/^}/ {print NR \"\"\"\": \"\"\"\" $0}' /Users/<USER>/Documents/zyb/extension/fwyy-tools/entrypoints/popup/main.ts)", "Bash(grep -n \"toggleBtn.disabled = false\" .output/chrome-mv3/chunks/popup-CWQ3h9sO.js)", "Bash(grep -r \"开始拦截\" .output/chrome-mv3/)", "Bash(grep -n \"saveData\" /Users/<USER>/Documents/zyb/extension/fwyy-tools/tools/api-migration-validator.ts)", "Bash(npx tsc --noEmit tools/api-migration-validator.ts)"], "deny": []}}